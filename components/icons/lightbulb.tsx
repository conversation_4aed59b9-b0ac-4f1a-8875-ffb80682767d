import { ComponentProps } from "react";

export default function LightbulbIcon(props: ComponentProps<"svg">) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x="0px"
      y="0px"
      viewBox="0 0 512 512"
      xmlSpace="preserve"
      fill="currentColor"
      {...props}
    >
      <path d="M139.325 118.114L96.903 75.692c-5.859-5.859-15.352-5.859-21.211 0s-5.859 15.352 0 21.211l42.422 42.422c5.859 5.859 15.352 5.859 21.211 0 5.859-5.859 5.859-15.352 0-21.211zM76 241H15c-8.291 0-15 6.709-15 15s6.709 15 15 15h61c8.291 0 15-6.709 15-15s-6.709-15-15-15zM497 241h-61c-8.291 0-15 6.709-15 15s6.709 15 15 15h61c8.291 0 15-6.709 15-15s-6.709-15-15-15zM436.308 75.692c-5.859-5.859-15.352-5.859-21.211 0l-42.422 42.422c-5.859 5.859-5.859 15.352 0 21.211 5.859 5.859 15.352 5.859 21.211 0l42.422-42.422c5.859-5.859 5.859-15.351 0-21.211zM256 0c-8.291 0-15 6.709-15 15v61c0 8.291 6.709 15 15 15s15-6.709 15-15V15c0-8.291-6.709-15-15-15zM340 150.099c-32.699-25.8-75-35.099-116.4-25.199-48 11.1-86.699 49.199-98.4 96.9-11.7 47.999 1.8 96.599 36.299 130.499C173.8 364.6 181 383.899 181 403.7v3.3c0 8.399 6.599 15 15 15h120c8.401 0 15-6.601 15-15v-3.3c0-19.501 7.5-39.401 20.7-52.301C376.901 325.899 391 292 391 256c0-41.4-18.6-80.101-51-105.901zM256 211c-22.63 0-39.104 15.011-43.418 32.388-1.963 7.903-9.915 12.947-18.179 10.957-8.027-1.992-12.935-10.137-10.942-18.164 7.432-30 35.61-55.181 72.539-55.181 8.291 0 15 6.709 15 15s-6.709 15-15 15zM196 452v15c0 24.814 20.186 45 45 45h30c24.814 0 45-20.186 45-45v-15H196z" />
    </svg>
  );
}
