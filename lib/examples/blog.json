{"content": "\"use client\";\n\nimport type React from \"react\";\n\nimport { useState } from \"react\";\n// @ts-ignore\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\n// @ts-ignore\nimport { Button } from \"@/components/ui/button\";\n// @ts-ignore\nimport { Input } from \"@/components/ui/input\";\n// @ts-ignore\nimport { Textarea } from \"@/components/ui/textarea\";\n// @ts-ignore\nimport { Badge } from \"@/components/ui/badge\";\n// @ts-ignore\nimport { Avatar, AvatarFallback } from \"@/components/ui/avatar\";\nimport { Calendar, Clock, Mail, MessageSquare, Send } from \"lucide-react\";\n\ninterface BlogPost {\n  id: string;\n  title: string;\n  excerpt: string;\n  content: string;\n  category: \"travel\" | \"food\" | \"technology\";\n  date: string;\n  readTime: string;\n  image: string;\n  comments: Comment[];\n}\n\ninterface Comment {\n  id: string;\n  author: string;\n  content: string;\n  date: string;\n  replies?: Comment[];\n}\n\nconst blogPosts: BlogPost[] = [\n  {\n    id: \"1\",\n    title: \"Exploring the Hidden Gems of Tokyo\",\n    excerpt:\n      \"Discover the lesser-known neighborhoods and authentic experiences that make Tokyo truly special.\",\n    content: `Tokyo is a city of endless discoveries. Beyond the famous landmarks like Shibuya Crossing and Tokyo Tower, there are countless hidden gems waiting to be explored. In this post, I'll share some of my favorite off-the-beaten-path locations that showcase the authentic spirit of this incredible city.\n\nOne of my favorite discoveries was the quiet neighborhood of Yanaka, where traditional wooden houses line narrow streets, and time seems to have stood still. The area is home to numerous temples, traditional shops, and cozy cafes that offer a glimpse into old Tokyo.\n\nAnother hidden gem is the Todoroki Valley, a surprising oasis of nature in the heart of the city. This narrow gorge offers a peaceful walking trail along a small stream, complete with a traditional tea house where you can rest and enjoy matcha while listening to the sound of flowing water.`,\n    category: \"travel\",\n    date: \"2024-01-15\",\n    readTime: \"5 min read\",\n    image: \"/tokyo-hidden-neighborhood-street-scene.png\",\n    comments: [\n      {\n        id: \"1\",\n        author: \"Sarah Chen\",\n        content:\n          \"Amazing post! I visited Yanaka last year and it was absolutely magical. Thanks for sharing these hidden spots.\",\n        date: \"2024-01-16\",\n      },\n    ],\n  },\n  {\n    id: \"2\",\n    title: \"The Art of Homemade Pasta: A Journey Through Italy\",\n    excerpt:\n      \"Learn the secrets behind authentic Italian pasta making from my culinary adventures across Italy.\",\n    content: `There's something magical about making pasta from scratch. During my recent trip to Italy, I had the privilege of learning from nonnas (grandmothers) in small villages who have been perfecting their craft for decades.\n\nThe key to great pasta lies in the simplicity of ingredients: just flour, eggs, and a pinch of salt. But the technique - that's where the magic happens. The way you knead the dough, the timing of when to roll it out, and the patience to let it rest properly all contribute to the final result.\n\nIn Emilia-Romagna, I learned to make tagliatelle with a traditional mattarello (rolling pin). The pasta sheets were rolled so thin you could read through them, yet they maintained the perfect texture when cooked. Each region has its own specialties and techniques, passed down through generations.`,\n    category: \"food\",\n    date: \"2024-01-10\",\n    readTime: \"7 min read\",\n    image: \"/homemade-pasta-making-italian-kitchen.png\",\n    comments: [\n      {\n        id: \"2\",\n        author: \"Marco Rossi\",\n        content:\n          \"As an Italian, I appreciate how you captured the essence of our pasta-making traditions. Brava!\",\n        date: \"2024-01-11\",\n      },\n    ],\n  },\n  {\n    id: \"3\",\n    title: \"The Future of Web Development: What to Expect in 2024\",\n    excerpt:\n      \"Exploring the latest trends and technologies shaping the future of web development.\",\n    content: `The web development landscape is evolving at an unprecedented pace. As we move through 2024, several key trends are reshaping how we build and interact with web applications.\n\nServer-side rendering is making a strong comeback with frameworks like Next.js and Remix leading the charge. The benefits of improved SEO, faster initial page loads, and better user experience are driving this shift back to the server.\n\nAI integration is becoming more prevalent, with tools like GitHub Copilot and ChatGPT changing how developers write code. We're also seeing the emergence of AI-powered design tools that can generate entire user interfaces from simple descriptions.\n\nWebAssembly continues to mature, enabling high-performance applications that were previously only possible with native development. This opens up new possibilities for complex applications running directly in the browser.`,\n    category: \"technology\",\n    date: \"2024-01-05\",\n    readTime: \"6 min read\",\n    image: \"/modern-web-development-coding-setup.png\",\n    comments: [],\n  },\n];\n\nconst categories = [\n  { id: \"all\", name: \"All Posts\", count: blogPosts.length },\n  {\n    id: \"travel\",\n    name: \"Travel\",\n    count: blogPosts.filter((post) => post.category === \"travel\").length,\n  },\n  {\n    id: \"food\",\n    name: \"Food\",\n    count: blogPosts.filter((post) => post.category === \"food\").length,\n  },\n  {\n    id: \"technology\",\n    name: \"Technology\",\n    count: blogPosts.filter((post) => post.category === \"technology\").length,\n  },\n];\n\nexport default function PersonalBlog() {\n  const [selectedCategory, setSelectedCategory] = useState(\"all\");\n  const [selectedPost, setSelectedPost] = useState<BlogPost | null>(null);\n  const [newComment, setNewComment] = useState(\"\");\n  const [commentAuthor, setCommentAuthor] = useState(\"\");\n  const [contactForm, setContactForm] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\",\n  });\n\n  const filteredPosts =\n    selectedCategory === \"all\"\n      ? blogPosts\n      : blogPosts.filter((post) => post.category === selectedCategory);\n\n  const handleCommentSubmit = (postId: string) => {\n    if (!newComment.trim() || !commentAuthor.trim()) return;\n\n    // In a real app, this would make an API call\n    console.log(\"[v0] Adding comment:\", {\n      postId,\n      author: commentAuthor,\n      content: newComment,\n    });\n    setNewComment(\"\");\n    setCommentAuthor(\"\");\n  };\n\n  const handleContactSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!contactForm.name || !contactForm.email || !contactForm.message) return;\n\n    // In a real app, this would make an API call\n    console.log(\"[v0] Contact form submitted:\", contactForm);\n    setContactForm({ name: \"\", email: \"\", message: \"\" });\n    alert(\"Thank you for your message! I'll get back to you soon.\");\n  };\n\n  if (selectedPost) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        {/* Header */}\n        <header className=\"sticky top-0 z-50 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n          <div className=\"mx-auto max-w-6xl px-4 py-4\">\n            <div className=\"flex items-center justify-between\">\n              <h1 className=\"text-2xl font-bold text-primary\">\n                My Personal Blog\n              </h1>\n              <Button variant=\"outline\" onClick={() => setSelectedPost(null)}>\n                Back to Blog\n              </Button>\n            </div>\n          </div>\n        </header>\n\n        {/* Blog Post Content */}\n        <main className=\"mx-auto max-w-4xl px-4 py-8\">\n          <article className=\"space-y-6\">\n            <div className=\"space-y-4\">\n              <Badge variant=\"secondary\" className=\"bg-primary/10 text-primary\">\n                {selectedPost.category}\n              </Badge>\n              <h1 className=\"text-4xl font-bold text-foreground\">\n                {selectedPost.title}\n              </h1>\n              <div className=\"flex items-center gap-4 text-muted-foreground\">\n                <div className=\"flex items-center gap-1\">\n                  <Calendar className=\"h-4 w-4\" />\n                  <span>\n                    {new Date(selectedPost.date).toLocaleDateString()}\n                  </span>\n                </div>\n                <div className=\"flex items-center gap-1\">\n                  <Clock className=\"h-4 w-4\" />\n                  <span>{selectedPost.readTime}</span>\n                </div>\n              </div>\n            </div>\n\n            <img\n              src={selectedPost.image || \"/placeholder.svg\"}\n              alt={selectedPost.title}\n              className=\"h-64 w-full rounded-lg object-cover\"\n            />\n\n            <div className=\"prose prose-lg max-w-none\">\n              {selectedPost.content.split(\"\\n\\n\").map((paragraph, index) => (\n                <p key={index} className=\"mb-4 leading-relaxed text-foreground\">\n                  {paragraph}\n                </p>\n              ))}\n            </div>\n          </article>\n\n          {/* Comments Section */}\n          <section className=\"mt-12 space-y-6\">\n            <h2 className=\"flex items-center gap-2 text-2xl font-bold text-foreground\">\n              <MessageSquare className=\"h-6 w-6\" />\n              Comments ({selectedPost.comments.length})\n            </h2>\n\n            {/* Add Comment Form */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Leave a Comment</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <Input\n                  placeholder=\"Your name\"\n                  value={commentAuthor}\n                  onChange={(e: {\n                    target: { value: React.SetStateAction<string> };\n                  }) => setCommentAuthor(e.target.value)}\n                />\n                <Textarea\n                  placeholder=\"Write your comment...\"\n                  value={newComment}\n                  onChange={(e: {\n                    target: { value: React.SetStateAction<string> };\n                  }) => setNewComment(e.target.value)}\n                  rows={4}\n                />\n                <Button\n                  onClick={() => handleCommentSubmit(selectedPost.id)}\n                  className=\"bg-primary hover:bg-primary/90\"\n                >\n                  <Send className=\"mr-2 h-4 w-4\" />\n                  Post Comment\n                </Button>\n              </CardContent>\n            </Card>\n\n            {/* Comments List */}\n            <div className=\"space-y-4\">\n              {selectedPost.comments.map((comment) => (\n                <Card key={comment.id}>\n                  <CardContent className=\"pt-6\">\n                    <div className=\"flex items-start gap-3\">\n                      <Avatar>\n                        <AvatarFallback>{comment.author[0]}</AvatarFallback>\n                      </Avatar>\n                      <div className=\"flex-1 space-y-2\">\n                        <div className=\"flex items-center gap-2\">\n                          <span className=\"font-semibold text-foreground\">\n                            {comment.author}\n                          </span>\n                          <span className=\"text-sm text-muted-foreground\">\n                            {new Date(comment.date).toLocaleDateString()}\n                          </span>\n                        </div>\n                        <p className=\"text-foreground\">{comment.content}</p>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          </section>\n        </main>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"sticky top-0 z-50 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div className=\"mx-auto max-w-6xl px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-2xl font-bold text-primary\">\n              My Personal Blog\n            </h1>\n            <nav className=\"hidden items-center gap-6 md:flex\">\n              <a\n                href=\"#about\"\n                className=\"text-foreground transition-colors hover:text-primary\"\n              >\n                About\n              </a>\n              <a\n                href=\"#blog\"\n                className=\"text-foreground transition-colors hover:text-primary\"\n              >\n                Blog\n              </a>\n              <a\n                href=\"#contact\"\n                className=\"text-foreground transition-colors hover:text-primary\"\n              >\n                Contact\n              </a>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"mx-auto max-w-6xl space-y-16 px-4 py-8\">\n        {/* Hero Section */}\n        <section className=\"space-y-8 py-12 text-center\">\n          <div className=\"space-y-6\">\n            <h1 className=\"text-5xl font-bold leading-tight text-foreground md:text-6xl\">\n              Welcome to My Blog\n            </h1>\n            <p className=\"mx-auto max-w-3xl text-xl leading-relaxed text-muted-foreground md:text-2xl\">\n              Discover stories about travel adventures, culinary experiences,\n              and the latest in technology. Join me on a journey of exploration\n              and discovery.\n            </p>\n            <div className=\"flex flex-wrap justify-center gap-4 pt-4\">\n              <Button\n                size=\"lg\"\n                className=\"bg-primary px-8 py-3 text-lg hover:bg-primary/90\"\n              >\n                Explore Posts\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"lg\"\n                className=\"bg-transparent px-8 py-3 text-lg\"\n              >\n                About Me\n              </Button>\n            </div>\n          </div>\n        </section>\n\n        {/* About Me Section */}\n        <section id=\"about\" className=\"space-y-6 text-center\">\n          <div className=\"space-y-4\">\n            <div>\n              <h2 className=\"mb-2 text-3xl font-bold text-foreground\">\n                Hi, I'm Jane Doe\n              </h2>\n              <p className=\"text-xl text-muted-foreground\">\n                Travel enthusiast, food lover, and tech explorer\n              </p>\n            </div>\n          </div>\n          <Card className=\"mx-auto max-w-2xl\">\n            <CardContent className=\"pt-6\">\n              <p className=\"leading-relaxed text-foreground\">\n                Welcome to my corner of the internet! I'm passionate about\n                exploring new places, discovering amazing food, and staying on\n                top of the latest technology trends. Through this blog, I share\n                my adventures, recipes, and insights from the ever-evolving\n                world of tech. Join me on this journey of discovery and\n                learning!\n              </p>\n            </CardContent>\n          </Card>\n        </section>\n\n        {/* Blog Section */}\n        <section id=\"blog\" className=\"space-y-8\">\n          <div className=\"space-y-2 text-center\">\n            <h2 className=\"text-3xl font-bold text-foreground\">Latest Posts</h2>\n            <p className=\"text-muted-foreground\">\n              Thoughts, stories, and ideas\n            </p>\n          </div>\n\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap justify-center gap-2\">\n            {categories.map((category) => (\n              <Button\n                key={category.id}\n                variant={\n                  selectedCategory === category.id ? \"default\" : \"outline\"\n                }\n                onClick={() => setSelectedCategory(category.id)}\n                className={\n                  selectedCategory === category.id\n                    ? \"bg-primary hover:bg-primary/90\"\n                    : \"\"\n                }\n              >\n                {category.name} ({category.count})\n              </Button>\n            ))}\n          </div>\n\n          {/* Blog Posts Grid */}\n          <div className=\"grid gap-8 md:grid-cols-2 lg:grid-cols-3\">\n            {filteredPosts.map((post) => (\n              <article\n                key={post.id}\n                className=\"group cursor-pointer\"\n                onClick={() => setSelectedPost(post)}\n              >\n                <Card className=\"h-full overflow-hidden border-0 bg-card shadow-md transition-all duration-300 hover:shadow-xl\">\n                  <div className=\"relative h-48 w-full overflow-hidden bg-muted\">\n                    <img\n                      src={post.image || \"/placeholder.svg\"}\n                      alt={post.title}\n                      className=\"absolute inset-0 h-full w-full object-cover transition-transform duration-500 group-hover:scale-105\"\n                    />\n                  </div>\n\n                  {/* Content Container */}\n                  <div className=\"space-y-4 p-6\">\n                    {/* Category and Read Time */}\n                    <div className=\"flex items-center justify-between\">\n                      <Badge\n                        variant=\"secondary\"\n                        className=\"bg-primary/10 text-primary transition-colors hover:bg-primary/20\"\n                      >\n                        {post.category}\n                      </Badge>\n                      <div className=\"flex items-center gap-1 text-sm text-muted-foreground\">\n                        <Clock className=\"h-3 w-3\" />\n                        <span>{post.readTime}</span>\n                      </div>\n                    </div>\n\n                    {/* Title */}\n                    <h3 className=\"line-clamp-2 text-xl font-bold leading-tight text-foreground transition-colors group-hover:text-primary\">\n                      {post.title}\n                    </h3>\n\n                    {/* Excerpt */}\n                    <p className=\"line-clamp-3 leading-relaxed text-muted-foreground\">\n                      {post.excerpt}\n                    </p>\n\n                    {/* Footer */}\n                    <div className=\"flex items-center justify-between border-t border-border/50 pt-2\">\n                      <div className=\"flex items-center gap-1 text-sm text-muted-foreground\">\n                        <Calendar className=\"h-3 w-3\" />\n                        <time dateTime={post.date}>\n                          {new Date(post.date).toLocaleDateString(\"en-US\", {\n                            month: \"short\",\n                            day: \"numeric\",\n                            year: \"numeric\",\n                          })}\n                        </time>\n                      </div>\n                      <div className=\"flex items-center gap-1 text-sm text-muted-foreground\">\n                        <MessageSquare className=\"h-3 w-3\" />\n                        <span>{post.comments.length} comments</span>\n                      </div>\n                    </div>\n                  </div>\n                </Card>\n              </article>\n            ))}\n          </div>\n        </section>\n\n        {/* Contact Section */}\n        <section id=\"contact\" className=\"space-y-8\">\n          <div className=\"space-y-2 text-center\">\n            <h2 className=\"text-3xl font-bold text-foreground\">Get In Touch</h2>\n            <p className=\"text-muted-foreground\">\n              Have a question or want to collaborate? I'd love to hear from you!\n            </p>\n          </div>\n\n          <Card className=\"mx-auto max-w-2xl\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Mail className=\"h-5 w-5\" />\n                Contact Me\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <form onSubmit={handleContactSubmit} className=\"space-y-4\">\n                <div className=\"grid gap-4 md:grid-cols-2\">\n                  <Input\n                    placeholder=\"Your Name\"\n                    value={contactForm.name}\n                    onChange={(e: { target: { value: any } }) =>\n                      setContactForm((prev) => ({\n                        ...prev,\n                        name: e.target.value,\n                      }))\n                    }\n                    required\n                  />\n                  <Input\n                    type=\"email\"\n                    placeholder=\"Your Email\"\n                    value={contactForm.email}\n                    onChange={(e: { target: { value: any } }) =>\n                      setContactForm((prev) => ({\n                        ...prev,\n                        email: e.target.value,\n                      }))\n                    }\n                    required\n                  />\n                </div>\n                <Textarea\n                  placeholder=\"Your Message\"\n                  value={contactForm.message}\n                  onChange={(e: { target: { value: any } }) =>\n                    setContactForm((prev) => ({\n                      ...prev,\n                      message: e.target.value,\n                    }))\n                  }\n                  rows={6}\n                  required\n                />\n                <Button\n                  type=\"submit\"\n                  className=\"w-full bg-primary hover:bg-primary/90\"\n                >\n                  <Send className=\"mr-2 h-4 w-4\" />\n                  Send Message\n                </Button>\n              </form>\n            </CardContent>\n          </Card>\n        </section>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"mt-16 bg-muted\">\n        <div className=\"mx-auto max-w-6xl px-4 py-8\">\n          <div className=\"space-y-4 text-center\">\n            <h3 className=\"text-lg font-semibold text-foreground\">Jane Doe</h3>\n            <p className=\"text-muted-foreground\">\n              Sharing stories from around the world, one post at a time.\n            </p>\n            <div className=\"flex justify-center gap-4\">\n              <Button variant=\"ghost\" size=\"sm\">\n                Twitter\n              </Button>\n              <Button variant=\"ghost\" size=\"sm\">\n                Instagram\n              </Button>\n              <Button variant=\"ghost\" size=\"sm\">\n                LinkedIn\n              </Button>\n            </div>\n            <p className=\"text-sm text-muted-foreground\">\n              © 2024 Jane Doe. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"}