{"content": "\"use client\";\n\nimport { useState, useEffect, useCallback } from \"react\";\n// @ts-ignore\nimport { Button } from \"@/components/ui/button\";\n// @ts-ignore\nimport { Card } from \"@/components/ui/card\";\n\nexport default function Calculator() {\n  const [display, setDisplay] = useState(\"0\");\n  const [previousValue, setPreviousValue] = useState<number | null>(null);\n  const [operation, setOperation] = useState<string | null>(null);\n  const [waitingForOperand, setWaitingForOperand] = useState(false);\n\n  const inputNumber = useCallback(\n    (num: string) => {\n      if (waitingForOperand) {\n        setDisplay(num);\n        setWaitingForOperand(false);\n      } else {\n        setDisplay(display === \"0\" ? num : display + num);\n      }\n    },\n    [display, waitingForOperand],\n  );\n\n  const inputDecimal = useCallback(() => {\n    if (waitingForOperand) {\n      setDisplay(\"0.\");\n      setWaitingForOperand(false);\n    } else if (display.indexOf(\".\") === -1) {\n      setDisplay(display + \".\");\n    }\n  }, [display, waitingForOperand]);\n\n  const clear = useCallback(() => {\n    setDisplay(\"0\");\n    setPreviousValue(null);\n    setOperation(null);\n    setWaitingForOperand(false);\n  }, []);\n\n  const performOperation = useCallback(\n    (nextOperation: string) => {\n      const inputValue = Number.parseFloat(display);\n\n      if (previousValue === null) {\n        setPreviousValue(inputValue);\n      } else if (operation) {\n        const currentValue = previousValue || 0;\n        const newValue = calculate(currentValue, inputValue, operation);\n\n        setDisplay(String(newValue));\n        setPreviousValue(newValue);\n      }\n\n      setWaitingForOperand(true);\n      setOperation(nextOperation);\n    },\n    [display, previousValue, operation],\n  );\n\n  const calculate = (\n    firstValue: number,\n    secondValue: number,\n    operation: string,\n  ): number => {\n    switch (operation) {\n      case \"+\":\n        return firstValue + secondValue;\n      case \"-\":\n        return firstValue - secondValue;\n      case \"×\":\n        return firstValue * secondValue;\n      case \"÷\":\n        return secondValue !== 0 ? firstValue / secondValue : 0;\n      default:\n        return secondValue;\n    }\n  };\n\n  const handleEquals = useCallback(() => {\n    const inputValue = Number.parseFloat(display);\n\n    if (previousValue !== null && operation) {\n      const newValue = calculate(previousValue, inputValue, operation);\n      setDisplay(String(newValue));\n      setPreviousValue(null);\n      setOperation(null);\n      setWaitingForOperand(true);\n    }\n  }, [display, previousValue, operation]);\n\n  // Keyboard support\n  useEffect(() => {\n    const handleKeyPress = (event: KeyboardEvent) => {\n      const { key } = event;\n\n      if (key >= \"0\" && key <= \"9\") {\n        inputNumber(key);\n      } else if (key === \".\") {\n        inputDecimal();\n      } else if (key === \"+\") {\n        performOperation(\"+\");\n      } else if (key === \"-\") {\n        performOperation(\"-\");\n      } else if (key === \"*\") {\n        performOperation(\"×\");\n      } else if (key === \"/\") {\n        event.preventDefault();\n        performOperation(\"÷\");\n      } else if (key === \"Enter\" || key === \"=\") {\n        handleEquals();\n      } else if (key === \"Escape\" || key === \"c\" || key === \"C\") {\n        clear();\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyPress);\n    return () => window.removeEventListener(\"keydown\", handleKeyPress);\n  }, [inputNumber, inputDecimal, performOperation, handleEquals, clear]);\n\n  const buttonClass =\n    \"h-16 text-lg font-semibold transition-all duration-150 active:scale-95\";\n  const numberButtonClass = `${buttonClass} bg-card hover:bg-muted border border-border text-card-foreground`;\n  const operationButtonClass = `${buttonClass} bg-secondary hover:bg-secondary/90 text-secondary-foreground`;\n  const clearButtonClass = `${buttonClass} bg-destructive hover:bg-destructive/90 text-destructive-foreground`;\n  const equalsButtonClass = `${buttonClass} bg-accent hover:bg-accent/90 text-accent-foreground`;\n\n  return (\n    <div className=\"flex min-h-screen items-center justify-center bg-background p-4\">\n      <Card className=\"w-full max-w-sm border border-border bg-card/80 p-6 shadow-2xl backdrop-blur-sm\">\n        <div className=\"space-y-4\">\n          {/* Display */}\n          <div className=\"rounded-lg border border-border bg-muted p-4\">\n            <div className=\"flex min-h-[2.5rem] items-center justify-end overflow-hidden text-right font-mono text-3xl font-bold text-foreground\">\n              {display}\n            </div>\n          </div>\n\n          {/* Button Grid */}\n          <div className=\"grid grid-cols-4 gap-3\">\n            {/* First Row */}\n            <Button\n              onClick={clear}\n              className={`${clearButtonClass} col-span-2`}\n            >\n              Clear\n            </Button>\n            <Button\n              onClick={() => performOperation(\"÷\")}\n              className={operationButtonClass}\n            >\n              ÷\n            </Button>\n            <Button\n              onClick={() => performOperation(\"×\")}\n              className={operationButtonClass}\n            >\n              ×\n            </Button>\n\n            {/* Second Row */}\n            <Button\n              onClick={() => inputNumber(\"7\")}\n              className={numberButtonClass}\n            >\n              7\n            </Button>\n            <Button\n              onClick={() => inputNumber(\"8\")}\n              className={numberButtonClass}\n            >\n              8\n            </Button>\n            <Button\n              onClick={() => inputNumber(\"9\")}\n              className={numberButtonClass}\n            >\n              9\n            </Button>\n            <Button\n              onClick={() => performOperation(\"-\")}\n              className={operationButtonClass}\n            >\n              −\n            </Button>\n\n            {/* Third Row */}\n            <Button\n              onClick={() => inputNumber(\"4\")}\n              className={numberButtonClass}\n            >\n              4\n            </Button>\n            <Button\n              onClick={() => inputNumber(\"5\")}\n              className={numberButtonClass}\n            >\n              5\n            </Button>\n            <Button\n              onClick={() => inputNumber(\"6\")}\n              className={numberButtonClass}\n            >\n              6\n            </Button>\n            <Button\n              onClick={() => performOperation(\"+\")}\n              className={operationButtonClass}\n            >\n              +\n            </Button>\n\n            {/* Fourth Row */}\n            <Button\n              onClick={() => inputNumber(\"1\")}\n              className={numberButtonClass}\n            >\n              1\n            </Button>\n            <Button\n              onClick={() => inputNumber(\"2\")}\n              className={numberButtonClass}\n            >\n              2\n            </Button>\n            <Button\n              onClick={() => inputNumber(\"3\")}\n              className={numberButtonClass}\n            >\n              3\n            </Button>\n            <Button\n              onClick={handleEquals}\n              className={`${equalsButtonClass} row-span-2`}\n            >\n              =\n            </Button>\n\n            {/* Fifth Row */}\n            <Button\n              onClick={() => inputNumber(\"0\")}\n              className={`${numberButtonClass} col-span-2`}\n            >\n              0\n            </Button>\n            <Button onClick={inputDecimal} className={numberButtonClass}>\n              .\n            </Button>\n          </div>\n        </div>\n\n        {/* Keyboard shortcuts hint */}\n        <div className=\"mt-4 text-center text-xs text-muted-foreground\">\n          Keyboard shortcuts: Numbers, +, -, *, /, Enter/=, Esc/C (clear)\n        </div>\n      </Card>\n    </div>\n  );\n}\n"}