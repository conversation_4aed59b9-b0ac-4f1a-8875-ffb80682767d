{"content": "\"use client\";\n\nimport { useState } from \"react\";\n// @ts-ignore\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n  // @ts-ignore\n} from \"@/components/ui/card\";\n// @ts-ignore\nimport { Badge } from \"@/components/ui/badge\";\nimport { CheckCircle, XCircle, RotateCcw } from \"lucide-react\";\n\ninterface Question {\n  id: number;\n  question: string;\n  options: string[];\n  correctAnswer: number;\n  explanation: string;\n}\n\nconst questions: Question[] = [\n  {\n    id: 1,\n    question: \"In what year did the American Revolution begin?\",\n    options: [\"1773\", \"1775\", \"1776\", \"1777\"],\n    correctAnswer: 1,\n    explanation:\n      \"The American Revolution began in 1775 with the Battles of Lexington and Concord on April 19, 1775. While the Declaration of Independence was signed in 1776, the actual fighting started a year earlier.\",\n  },\n  {\n    id: 2,\n    question: \"Who was the first President of the United States?\",\n    options: [\n      \"<PERSON>\",\n      \"<PERSON>\",\n      \"<PERSON>\",\n      \"<PERSON>\",\n    ],\n    correctAnswer: 2,\n    explanation:\n      \"<PERSON> was unanimously elected as the first President of the United States in 1789. He served two terms and established many precedents for future presidents.\",\n  },\n  {\n    id: 3,\n    question: \"Which purchase doubled the size of the United States in 1803?\",\n    options: [\n      \"Alaska Purchase\",\n      \"Louisiana Purchase\",\n      \"Gadsden Purchase\",\n      \"Texas Annexation\",\n    ],\n    correctAnswer: 1,\n    explanation:\n      \"The Louisiana Purchase in 1803 doubled the size of the United States. President Thomas Jefferson bought this vast territory from France for $15 million, adding about 827,000 square miles to the nation.\",\n  },\n  {\n    id: 4,\n    question: \"The Civil War lasted from 1861 to what year?\",\n    options: [\"1863\", \"1864\", \"1865\", \"1866\"],\n    correctAnswer: 2,\n    explanation:\n      \"The American Civil War ended in 1865 when Confederate General Robert E. Lee surrendered to Union General Ulysses S. Grant at Appomattox Court House on April 9, 1865.\",\n  },\n  {\n    id: 5,\n    question: \"Which amendment gave women the right to vote?\",\n    options: [\n      \"17th Amendment\",\n      \"18th Amendment\",\n      \"19th Amendment\",\n      \"20th Amendment\",\n    ],\n    correctAnswer: 2,\n    explanation:\n      \"The 19th Amendment, ratified on August 18, 1920, granted women the right to vote. This was the culmination of decades of activism by suffragettes like Susan B. Anthony and Elizabeth Cady Stanton.\",\n  },\n  {\n    id: 6,\n    question: \"Who was President during the Great Depression?\",\n    options: [\n      \"Herbert Hoover\",\n      \"Franklin D. Roosevelt\",\n      \"Harry Truman\",\n      \"Dwight Eisenhower\",\n    ],\n    correctAnswer: 1,\n    explanation:\n      \"Franklin D. Roosevelt was President during most of the Great Depression (1933-1945). He implemented the New Deal programs to help recover from the economic crisis that began under Herbert Hoover's presidency.\",\n  },\n  {\n    id: 7,\n    question: \"Which event brought the United States into World War II?\",\n    options: [\n      \"Invasion of Poland\",\n      \"Battle of Britain\",\n      \"Pearl Harbor Attack\",\n      \"D-Day Invasion\",\n    ],\n    correctAnswer: 2,\n    explanation:\n      \"The attack on Pearl Harbor on December 7, 1941, brought the United States into World War II. The surprise attack by Japan killed over 2,400 Americans and prompted Congress to declare war the next day.\",\n  },\n  {\n    id: 8,\n    question: \"Who delivered the famous 'I Have a Dream' speech?\",\n    options: [\n      \"Malcolm X\",\n      \"Martin Luther King Jr.\",\n      \"Rosa Parks\",\n      \"John Lewis\",\n    ],\n    correctAnswer: 1,\n    explanation:\n      \"Martin Luther King Jr. delivered the iconic 'I Have a Dream' speech on August 28, 1963, during the March on Washington for Jobs and Freedom. This speech became a defining moment of the Civil Rights Movement.\",\n  },\n];\n\nexport default function AmericanHistoryQuiz() {\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);\n  const [showExplanation, setShowExplanation] = useState(false);\n  const [score, setScore] = useState(0);\n  const [quizCompleted, setQuizCompleted] = useState(false);\n  const [answeredQuestions, setAnsweredQuestions] = useState<boolean[]>(\n    new Array(questions.length).fill(false),\n  );\n\n  const handleAnswerSelect = (answerIndex: number) => {\n    if (showExplanation) return;\n    setSelectedAnswer(answerIndex);\n  };\n\n  const handleSubmitAnswer = () => {\n    if (selectedAnswer === null) return;\n\n    setShowExplanation(true);\n\n    // Update answered questions\n    const newAnsweredQuestions = [...answeredQuestions];\n    newAnsweredQuestions[currentQuestion] = true;\n    setAnsweredQuestions(newAnsweredQuestions);\n\n    // Update score if correct\n    if (selectedAnswer === questions[currentQuestion].correctAnswer) {\n      setScore(score + 1);\n    }\n  };\n\n  const handleNextQuestion = () => {\n    if (currentQuestion < questions.length - 1) {\n      setCurrentQuestion(currentQuestion + 1);\n      setSelectedAnswer(null);\n      setShowExplanation(false);\n    } else {\n      setQuizCompleted(true);\n    }\n  };\n\n  const resetQuiz = () => {\n    setCurrentQuestion(0);\n    setSelectedAnswer(null);\n    setShowExplanation(false);\n    setScore(0);\n    setQuizCompleted(false);\n    setAnsweredQuestions(new Array(questions.length).fill(false));\n  };\n\n  const getScoreColor = () => {\n    const percentage = (score / questions.length) * 100;\n    if (percentage >= 80) return \"bg-green-500\";\n    if (percentage >= 60) return \"bg-yellow-500\";\n    return \"bg-red-500\";\n  };\n\n  const getScoreMessage = () => {\n    const percentage = (score / questions.length) * 100;\n    if (percentage >= 80) return \"Excellent! You know your American history!\";\n    if (percentage >= 60)\n      return \"Good job! You have a solid understanding of American history.\";\n    if (percentage >= 40)\n      return \"Not bad! Consider reviewing some key events in American history.\";\n    return \"Keep studying! American history has many fascinating stories to discover.\";\n  };\n\n  if (quizCompleted) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center bg-gradient-to-br from-blue-50 to-red-50 p-4\">\n        <Card className=\"w-full max-w-2xl\">\n          <CardHeader className=\"text-center\">\n            <CardTitle className=\"text-3xl font-bold text-blue-800\">\n              Quiz Complete!\n            </CardTitle>\n            <CardDescription className=\"text-lg\">\n              Here are your results\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            <div className=\"text-center\">\n              <div\n                className={`inline-flex h-24 w-24 items-center justify-center rounded-full ${getScoreColor()} mb-4 text-2xl font-bold text-white`}\n              >\n                {score}/{questions.length}\n              </div>\n              <p className=\"mb-2 text-xl font-semibold\">\n                You scored {Math.round((score / questions.length) * 100)}%\n              </p>\n              <p className=\"text-muted-foreground\">{getScoreMessage()}</p>\n            </div>\n\n            <div className=\"space-y-2\">\n              <h3 className=\"text-lg font-semibold\">Question Breakdown:</h3>\n              <div className=\"grid grid-cols-4 gap-2\">\n                {questions.map((_, index) => (\n                  <div\n                    key={index}\n                    className={`rounded p-2 text-center text-sm font-medium ${\n                      answeredQuestions[index]\n                        ? \"bg-green-100 text-green-800\"\n                        : \"bg-gray-100 text-gray-600\"\n                    }`}\n                  >\n                    Q{index + 1}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <Button onClick={resetQuiz} className=\"w-full\" size=\"lg\">\n              <RotateCcw className=\"mr-2 h-4 w-4\" />\n              Take Quiz Again\n            </Button>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  const question = questions[currentQuestion];\n  const isCorrect = selectedAnswer === question.correctAnswer;\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-red-50 p-4\">\n      <div className=\"mx-auto max-w-4xl\">\n        {/* Header */}\n        <div className=\"mb-8 text-center\">\n          <h1 className=\"mb-2 text-4xl font-bold text-blue-800\">\n            American History Quiz\n          </h1>\n          <p className=\"text-muted-foreground\">\n            Test your knowledge of American history\n          </p>\n        </div>\n\n        {/* Progress and Score */}\n        <div className=\"mb-6 flex items-center justify-between\">\n          <Badge variant=\"outline\" className=\"text-sm\">\n            Question {currentQuestion + 1} of {questions.length}\n          </Badge>\n          <Badge variant=\"secondary\" className=\"text-sm\">\n            Score: {score}/{questions.length}\n          </Badge>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"mb-8 h-2 w-full rounded-full bg-gray-200\">\n          <div\n            className=\"h-2 rounded-full bg-blue-600 transition-all duration-300\"\n            style={{\n              width: `${((currentQuestion + 1) / questions.length) * 100}%`,\n            }}\n          ></div>\n        </div>\n\n        {/* Question Card */}\n        <Card className=\"mb-6\">\n          <CardHeader>\n            <CardTitle className=\"text-xl\">{question.question}</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            {/* Answer Options */}\n            <div className=\"grid gap-3\">\n              {question.options.map((option, index) => (\n                <button\n                  key={index}\n                  onClick={() => handleAnswerSelect(index)}\n                  disabled={showExplanation}\n                  className={`rounded-lg border-2 p-4 text-left transition-all duration-200 ${\n                    selectedAnswer === index\n                      ? showExplanation\n                        ? index === question.correctAnswer\n                          ? \"border-green-500 bg-green-50 text-green-800\"\n                          : \"border-red-500 bg-red-50 text-red-800\"\n                        : \"border-blue-500 bg-blue-50\"\n                      : showExplanation && index === question.correctAnswer\n                        ? \"border-green-500 bg-green-50 text-green-800\"\n                        : \"border-gray-200 hover:border-gray-300 hover:bg-gray-50\"\n                  } ${showExplanation ? \"cursor-default\" : \"cursor-pointer\"}`}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <span>{option}</span>\n                    {showExplanation && (\n                      <div>\n                        {index === question.correctAnswer && (\n                          <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                        )}\n                        {selectedAnswer === index &&\n                          index !== question.correctAnswer && (\n                            <XCircle className=\"h-5 w-5 text-red-600\" />\n                          )}\n                      </div>\n                    )}\n                  </div>\n                </button>\n              ))}\n            </div>\n\n            {/* Explanation */}\n            {showExplanation && (\n              <div\n                className={`rounded-lg p-4 ${isCorrect ? \"border border-green-200 bg-green-50\" : \"border border-red-200 bg-red-50\"}`}\n              >\n                <div className=\"flex items-start gap-3\">\n                  {isCorrect ? (\n                    <CheckCircle className=\"mt-0.5 h-6 w-6 flex-shrink-0 text-green-600\" />\n                  ) : (\n                    <XCircle className=\"mt-0.5 h-6 w-6 flex-shrink-0 text-red-600\" />\n                  )}\n                  <div>\n                    <p\n                      className={`mb-2 font-semibold ${isCorrect ? \"text-green-800\" : \"text-red-800\"}`}\n                    >\n                      {isCorrect ? \"Correct!\" : \"Incorrect!\"}\n                    </p>\n                    <p className=\"text-gray-700\">{question.explanation}</p>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Action Buttons */}\n            <div className=\"flex gap-3 pt-4\">\n              {!showExplanation ? (\n                <Button\n                  onClick={handleSubmitAnswer}\n                  disabled={selectedAnswer === null}\n                  className=\"flex-1\"\n                >\n                  Submit Answer\n                </Button>\n              ) : (\n                <Button onClick={handleNextQuestion} className=\"flex-1\">\n                  {currentQuestion < questions.length - 1\n                    ? \"Next Question\"\n                    : \"View Results\"}\n                </Button>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"}