{"content": "\"use client\";\n// @ts-ignore\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n  // @ts-ignore\n} from \"@/components/ui/card\";\n// @ts-ignore\nimport { Input } from \"@/components/ui/input\";\nimport {\n  Menu,\n  X,\n  ArrowRight,\n  Play,\n  Zap,\n  Users,\n  BarChart3,\n  Shield,\n  Star,\n  Check,\n  Facebook,\n  Twitter,\n  Linkedin,\n  Github,\n} from \"lucide-react\";\nimport { useState } from \"react\";\n\nexport default function HomePage() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const features = [\n    {\n      icon: Zap,\n      title: \"Intelligent Automation\",\n      description:\n        \"Automate repetitive tasks and workflows with AI-powered intelligence that learns from your team's patterns.\",\n    },\n    {\n      icon: Users,\n      title: \"Seamless Collaboration\",\n      description:\n        \"Connect your team with real-time collaboration tools, shared workspaces, and instant communication.\",\n    },\n    {\n      icon: BarChart3,\n      title: \"Advanced Analytics\",\n      description:\n        \"Get deep insights into your workflow performance with comprehensive analytics and reporting.\",\n    },\n    {\n      icon: Shield,\n      title: \"Enterprise Security\",\n      description:\n        \"Keep your data secure with enterprise-grade encryption, compliance, and access controls.\",\n    },\n  ];\n\n  const testimonials = [\n    {\n      name: \"<PERSON>\",\n      role: \"Product Manager\",\n      company: \"TechCorp\",\n      content:\n        \"StreamLine has revolutionized how our team collaborates. We've seen a 40% increase in productivity since implementing it.\",\n      rating: 5,\n    },\n    {\n      name: \"Michael Chen\",\n      role: \"Operations Director\",\n      company: \"InnovateLab\",\n      content:\n        \"The automation features are incredible. What used to take hours now happens automatically. It's a game-changer.\",\n      rating: 5,\n    },\n    {\n      name: \"Emily Rodriguez\",\n      role: \"Team Lead\",\n      company: \"FlowWorks\",\n      content:\n        \"The analytics dashboard gives us insights we never had before. We can now make data-driven decisions with confidence.\",\n      rating: 5,\n    },\n  ];\n\n  const plans = [\n    {\n      name: \"Starter\",\n      price: \"$29\",\n      period: \"/month\",\n      description: \"Perfect for small teams getting started\",\n      features: [\n        \"Up to 5 team members\",\n        \"Basic automation workflows\",\n        \"Standard integrations\",\n        \"Email support\",\n        \"5GB storage\",\n      ],\n      popular: false,\n    },\n    {\n      name: \"Professional\",\n      price: \"$79\",\n      period: \"/month\",\n      description: \"Ideal for growing teams and businesses\",\n      features: [\n        \"Up to 25 team members\",\n        \"Advanced automation workflows\",\n        \"Premium integrations\",\n        \"Priority support\",\n        \"100GB storage\",\n        \"Advanced analytics\",\n      ],\n      popular: true,\n    },\n    {\n      name: \"Enterprise\",\n      price: \"$199\",\n      period: \"/month\",\n      description: \"For large organizations with complex needs\",\n      features: [\n        \"Unlimited team members\",\n        \"Custom automation workflows\",\n        \"All integrations\",\n        \"24/7 dedicated support\",\n        \"Unlimited storage\",\n        \"Advanced analytics & reporting\",\n        \"Custom security controls\",\n      ],\n      popular: false,\n    },\n  ];\n\n  return (\n    <main className=\"min-h-screen\">\n      {/* Header */}\n      <header className=\"sticky top-0 z-50 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex h-16 items-center justify-between\">\n            {/* Logo */}\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <h1 className=\"text-2xl font-bold text-primary\">StreamLine</h1>\n              </div>\n            </div>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden md:block\">\n              <div className=\"ml-10 flex items-baseline space-x-8\">\n                <a\n                  href=\"#features\"\n                  className=\"text-foreground transition-colors hover:text-primary\"\n                >\n                  Features\n                </a>\n                <a\n                  href=\"#pricing\"\n                  className=\"text-foreground transition-colors hover:text-primary\"\n                >\n                  Pricing\n                </a>\n                <a\n                  href=\"#testimonials\"\n                  className=\"text-foreground transition-colors hover:text-primary\"\n                >\n                  Resources\n                </a>\n                <a\n                  href=\"#contact\"\n                  className=\"text-foreground transition-colors hover:text-primary\"\n                >\n                  Contact\n                </a>\n              </div>\n            </nav>\n\n            {/* CTA Button */}\n            <div className=\"hidden md:block\">\n              <Button className=\"bg-primary text-primary-foreground hover:bg-primary/90\">\n                Get Started\n              </Button>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-foreground hover:text-primary\"\n              >\n                {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n              </button>\n            </div>\n          </div>\n\n          {/* Mobile Navigation */}\n          {isMenuOpen && (\n            <div className=\"md:hidden\">\n              <div className=\"space-y-1 border-t border-border px-2 pb-3 pt-2 sm:px-3\">\n                <a\n                  href=\"#features\"\n                  className=\"block px-3 py-2 text-foreground hover:text-primary\"\n                >\n                  Features\n                </a>\n                <a\n                  href=\"#pricing\"\n                  className=\"block px-3 py-2 text-foreground hover:text-primary\"\n                >\n                  Pricing\n                </a>\n                <a\n                  href=\"#testimonials\"\n                  className=\"block px-3 py-2 text-foreground hover:text-primary\"\n                >\n                  Resources\n                </a>\n                <a\n                  href=\"#contact\"\n                  className=\"block px-3 py-2 text-foreground hover:text-primary\"\n                >\n                  Contact\n                </a>\n                <div className=\"px-3 py-2\">\n                  <Button className=\"w-full bg-primary text-primary-foreground hover:bg-primary/90\">\n                    Get Started\n                  </Button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"relative overflow-hidden py-20 lg:py-32\">\n        {/* Background Pattern */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-card to-background opacity-50\"></div>\n\n        <div className=\"container relative mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"mx-auto max-w-4xl text-center\">\n            <h1 className=\"mb-6 text-4xl font-black leading-tight text-foreground sm:text-5xl lg:text-6xl\">\n              Streamline Your Workflow,{\" \"}\n              <span className=\"text-primary\">Amplify Your Success</span>\n            </h1>\n\n            <p className=\"mx-auto mb-8 max-w-2xl text-xl leading-relaxed text-muted-foreground\">\n              Transform your team's productivity with intelligent automation,\n              seamless collaboration, and powerful insights that drive results.\n            </p>\n\n            <div className=\"flex flex-col items-center justify-center gap-4 sm:flex-row\">\n              <Button\n                size=\"lg\"\n                className=\"bg-primary px-8 py-3 text-primary-foreground hover:bg-primary/90\"\n              >\n                Try for Free\n                <ArrowRight className=\"ml-2 h-5 w-5\" />\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"lg\"\n                className=\"border-primary bg-transparent px-8 py-3 text-primary hover:bg-primary hover:text-primary-foreground\"\n              >\n                <Play className=\"mr-2 h-5 w-5\" />\n                Watch Demo\n              </Button>\n            </div>\n\n            <div className=\"mt-12\">\n              <p className=\"mb-4 text-sm text-muted-foreground\">\n                Trusted by 10,000+ teams worldwide\n              </p>\n              <div className=\"flex items-center justify-center space-x-8 opacity-60\">\n                <div className=\"text-lg font-semibold\">TechCorp</div>\n                <div className=\"text-lg font-semibold\">InnovateLab</div>\n                <div className=\"text-lg font-semibold\">FlowWorks</div>\n                <div className=\"text-lg font-semibold\">DataSync</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section id=\"features\" className=\"bg-muted/30 py-20\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"mb-16 text-center\">\n            <h2 className=\"mb-4 text-3xl font-black text-foreground sm:text-4xl\">\n              Powerful Features for Modern Teams\n            </h2>\n            <p className=\"mx-auto max-w-2xl text-xl text-muted-foreground\">\n              Everything you need to streamline your workflow and boost\n              productivity\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4\">\n            {features.map((feature, index) => (\n              <Card\n                key={index}\n                className=\"border-border transition-shadow hover:shadow-lg\"\n              >\n                <CardHeader>\n                  <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10\">\n                    <feature.icon className=\"h-6 w-6 text-primary\" />\n                  </div>\n                  <CardTitle className=\"text-xl font-bold\">\n                    {feature.title}\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <CardDescription className=\"leading-relaxed text-muted-foreground\">\n                    {feature.description}\n                  </CardDescription>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Testimonials Section */}\n      <section id=\"testimonials\" className=\"py-20\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"mb-16 text-center\">\n            <h2 className=\"mb-4 text-3xl font-black text-foreground sm:text-4xl\">\n              What Our Customers Say\n            </h2>\n            <p className=\"mx-auto max-w-2xl text-xl text-muted-foreground\">\n              Join thousands of satisfied teams who have transformed their\n              workflows\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 gap-8 md:grid-cols-3\">\n            {testimonials.map((testimonial, index) => (\n              <Card key={index} className=\"border-border\">\n                <CardContent className=\"p-6\">\n                  <div className=\"mb-4 flex\">\n                    {[...Array(testimonial.rating)].map((_, i) => (\n                      <Star\n                        key={i}\n                        className=\"h-5 w-5 fill-primary text-primary\"\n                      />\n                    ))}\n                  </div>\n                  <blockquote className=\"mb-4 leading-relaxed text-foreground\">\n                    \"{testimonial.content}\"\n                  </blockquote>\n                  <div className=\"border-t border-border pt-4\">\n                    <div className=\"font-semibold text-foreground\">\n                      {testimonial.name}\n                    </div>\n                    <div className=\"text-sm text-muted-foreground\">\n                      {testimonial.role} at {testimonial.company}\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Pricing Section */}\n      <section id=\"pricing\" className=\"bg-muted/30 py-20\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"mb-16 text-center\">\n            <h2 className=\"mb-4 text-3xl font-black text-foreground sm:text-4xl\">\n              Simple, Transparent Pricing\n            </h2>\n            <p className=\"mx-auto max-w-2xl text-xl text-muted-foreground\">\n              Choose the plan that fits your team's needs. All plans include a\n              14-day free trial.\n            </p>\n          </div>\n\n          <div className=\"mx-auto grid max-w-6xl grid-cols-1 gap-8 md:grid-cols-3\">\n            {plans.map((plan, index) => (\n              <Card\n                key={index}\n                className={`relative border-border ${plan.popular ? \"scale-105 shadow-lg ring-2 ring-primary\" : \"\"}`}\n              >\n                {plan.popular && (\n                  <div className=\"absolute -top-3 left-1/2 -translate-x-1/2 transform\">\n                    <span className=\"rounded-full bg-primary px-4 py-1 text-sm font-semibold text-primary-foreground\">\n                      Most Popular\n                    </span>\n                  </div>\n                )}\n\n                <CardHeader className=\"pb-8 text-center\">\n                  <CardTitle className=\"text-2xl font-bold\">\n                    {plan.name}\n                  </CardTitle>\n                  <div className=\"mt-4\">\n                    <span className=\"text-4xl font-black text-primary\">\n                      {plan.price}\n                    </span>\n                    <span className=\"text-muted-foreground\">{plan.period}</span>\n                  </div>\n                  <CardDescription className=\"mt-2\">\n                    {plan.description}\n                  </CardDescription>\n                </CardHeader>\n\n                <CardContent>\n                  <ul className=\"mb-8 space-y-3\">\n                    {plan.features.map((feature, featureIndex) => (\n                      <li key={featureIndex} className=\"flex items-center\">\n                        <Check className=\"mr-3 h-5 w-5 flex-shrink-0 text-primary\" />\n                        <span className=\"text-foreground\">{feature}</span>\n                      </li>\n                    ))}\n                  </ul>\n\n                  <Button\n                    className={`w-full ${\n                      plan.popular\n                        ? \"bg-primary text-primary-foreground hover:bg-primary/90\"\n                        : \"bg-secondary text-secondary-foreground hover:bg-secondary/90\"\n                    }`}\n                  >\n                    Start Free Trial\n                  </Button>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-primary/5 py-20\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"mx-auto max-w-4xl text-center\">\n            <h2 className=\"mb-4 text-3xl font-black text-foreground sm:text-4xl\">\n              Ready to Transform Your Workflow?\n            </h2>\n            <p className=\"mx-auto mb-8 max-w-2xl text-xl text-muted-foreground\">\n              Join thousands of teams who have already streamlined their\n              processes. Start your free trial today and see the difference.\n            </p>\n\n            <div className=\"mx-auto flex max-w-md flex-col items-center justify-center gap-4 sm:flex-row\">\n              <Input\n                type=\"email\"\n                placeholder=\"Enter your work email\"\n                className=\"flex-1 border-border bg-background\"\n              />\n              <Button className=\"bg-primary px-6 text-primary-foreground hover:bg-primary/90\">\n                Get Started\n                <ArrowRight className=\"ml-2 h-4 w-4\" />\n              </Button>\n            </div>\n\n            <p className=\"mt-4 text-sm text-muted-foreground\">\n              No credit card required • 14-day free trial • Cancel anytime\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer id=\"contact\" className=\"border-t border-border bg-card\">\n        <div className=\"container mx-auto px-4 py-12 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 gap-8 md:grid-cols-4\">\n            {/* Company Info */}\n            <div className=\"col-span-1 md:col-span-2\">\n              <h3 className=\"mb-4 text-2xl font-bold text-primary\">\n                StreamLine\n              </h3>\n              <p className=\"mb-4 max-w-md text-muted-foreground\">\n                Empowering teams worldwide to streamline their workflows and\n                achieve more with intelligent automation and seamless\n                collaboration.\n              </p>\n              <div className=\"flex space-x-4\">\n                <a\n                  href=\"#\"\n                  className=\"text-muted-foreground transition-colors hover:text-primary\"\n                >\n                  <Facebook className=\"h-5 w-5\" />\n                </a>\n                <a\n                  href=\"#\"\n                  className=\"text-muted-foreground transition-colors hover:text-primary\"\n                >\n                  <Twitter className=\"h-5 w-5\" />\n                </a>\n                <a\n                  href=\"#\"\n                  className=\"text-muted-foreground transition-colors hover:text-primary\"\n                >\n                  <Linkedin className=\"h-5 w-5\" />\n                </a>\n                <a\n                  href=\"#\"\n                  className=\"text-muted-foreground transition-colors hover:text-primary\"\n                >\n                  <Github className=\"h-5 w-5\" />\n                </a>\n              </div>\n            </div>\n\n            {/* Product Links */}\n            <div>\n              <h4 className=\"mb-4 font-semibold text-foreground\">Product</h4>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a\n                    href=\"#\"\n                    className=\"text-muted-foreground transition-colors hover:text-primary\"\n                  >\n                    Features\n                  </a>\n                </li>\n                <li>\n                  <a\n                    href=\"#\"\n                    className=\"text-muted-foreground transition-colors hover:text-primary\"\n                  >\n                    Pricing\n                  </a>\n                </li>\n                <li>\n                  <a\n                    href=\"#\"\n                    className=\"text-muted-foreground transition-colors hover:text-primary\"\n                  >\n                    Integrations\n                  </a>\n                </li>\n                <li>\n                  <a\n                    href=\"#\"\n                    className=\"text-muted-foreground transition-colors hover:text-primary\"\n                  >\n                    API\n                  </a>\n                </li>\n              </ul>\n            </div>\n\n            {/* Company Links */}\n            <div>\n              <h4 className=\"mb-4 font-semibold text-foreground\">Company</h4>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a\n                    href=\"#\"\n                    className=\"text-muted-foreground transition-colors hover:text-primary\"\n                  >\n                    About\n                  </a>\n                </li>\n                <li>\n                  <a\n                    href=\"#\"\n                    className=\"text-muted-foreground transition-colors hover:text-primary\"\n                  >\n                    Blog\n                  </a>\n                </li>\n                <li>\n                  <a\n                    href=\"#\"\n                    className=\"text-muted-foreground transition-colors hover:text-primary\"\n                  >\n                    Careers\n                  </a>\n                </li>\n                <li>\n                  <a\n                    href=\"#\"\n                    className=\"text-muted-foreground transition-colors hover:text-primary\"\n                  >\n                    Contact\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n\n          <div className=\"mt-8 flex flex-col items-center justify-between border-t border-border pt-8 sm:flex-row\">\n            <p className=\"text-sm text-muted-foreground\">\n              © 2025 StreamLine. All rights reserved.\n            </p>\n            <div className=\"mt-4 flex space-x-6 sm:mt-0\">\n              <a\n                href=\"#\"\n                className=\"text-sm text-muted-foreground transition-colors hover:text-primary\"\n              >\n                Privacy Policy\n              </a>\n              <a\n                href=\"#\"\n                className=\"text-sm text-muted-foreground transition-colors hover:text-primary\"\n              >\n                Terms of Service\n              </a>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </main>\n  );\n}\n"}