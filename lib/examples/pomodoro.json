{"content": "\"use client\";\n\nimport { useState, useEffect, useRef } from \"react\";\n// @ts-ignore\nimport { But<PERSON> } from \"@/components/ui/button\";\n// @ts-ignore\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\n// @ts-ignore\nimport { Badge } from \"@/components/ui/badge\";\nimport { Play, Pause, RotateCcw, Coffee, Brain } from \"lucide-react\";\n\ntype TimerMode = \"work\" | \"shortBreak\" | \"longBreak\";\n\nconst TIMER_DURATIONS = {\n  work: 25 * 60, // 25 minutes\n  shortBreak: 5 * 60, // 5 minutes\n  longBreak: 15 * 60, // 15 minutes\n};\n\nconst MODE_LABELS = {\n  work: \"Focus Time\",\n  shortBreak: \"Short Break\",\n  longBreak: \"Long Break\",\n};\n\nconst MODE_COLORS = {\n  work: \"bg-red-500\",\n  shortBreak: \"bg-green-500\",\n  longBreak: \"bg-blue-500\",\n};\n\nexport default function PomodoroTimer() {\n  const [mode, setMode] = useState<TimerMode>(\"work\");\n  const [timeLeft, setTimeLeft] = useState(TIMER_DURATIONS.work);\n  const [isRunning, setIsRunning] = useState(false);\n  const [completedSessions, setCompletedSessions] = useState(0);\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n  const audioRef = useRef<HTMLAudioElement | null>(null);\n\n  // Initialize audio\n  useEffect(() => {\n    audioRef.current = new Audio(\n      \"data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT\",\n    );\n  }, []);\n\n  // Timer logic\n  useEffect(() => {\n    if (isRunning && timeLeft > 0) {\n      intervalRef.current = setInterval(() => {\n        setTimeLeft((prev) => prev - 1);\n      }, 1000);\n    } else {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    }\n\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, [isRunning, timeLeft]);\n\n  // Handle timer completion\n  useEffect(() => {\n    if (timeLeft === 0 && isRunning) {\n      setIsRunning(false);\n\n      // Play notification sound\n      if (audioRef.current) {\n        audioRef.current.play().catch(() => {\n          // Ignore audio play errors (browser restrictions)\n        });\n      }\n\n      if (mode === \"work\") {\n        setCompletedSessions((prev) => prev + 1);\n        // After 4 work sessions, take a long break\n        const nextMode =\n          (completedSessions + 1) % 4 === 0 ? \"longBreak\" : \"shortBreak\";\n        setMode(nextMode);\n        setTimeLeft(TIMER_DURATIONS[nextMode]);\n      } else {\n        // Break finished, back to work\n        setMode(\"work\");\n        setTimeLeft(TIMER_DURATIONS.work);\n      }\n    }\n  }, [timeLeft, isRunning, mode, completedSessions]);\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, \"0\")}:${secs.toString().padStart(2, \"0\")}`;\n  };\n\n  const toggleTimer = () => {\n    setIsRunning(!isRunning);\n  };\n\n  const resetTimer = () => {\n    setIsRunning(false);\n    setTimeLeft(TIMER_DURATIONS[mode]);\n  };\n\n  const switchMode = (newMode: TimerMode) => {\n    setIsRunning(false);\n    setMode(newMode);\n    setTimeLeft(TIMER_DURATIONS[newMode]);\n  };\n\n  const progress =\n    ((TIMER_DURATIONS[mode] - timeLeft) / TIMER_DURATIONS[mode]) * 100;\n\n  return (\n    <div className=\"flex min-h-screen items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 p-4 dark:from-slate-900 dark:to-slate-800\">\n      <Card className=\"mx-auto w-full max-w-md shadow-2xl\">\n        <CardHeader className=\"pb-4 text-center\">\n          <CardTitle className=\"flex items-center justify-center gap-2 text-2xl font-bold\">\n            {mode === \"work\" ? (\n              <Brain className=\"h-6 w-6\" />\n            ) : (\n              <Coffee className=\"h-6 w-6\" />\n            )}\n            Pomodoro Timer\n          </CardTitle>\n          <Badge variant=\"secondary\" className=\"mx-auto\">\n            Session {completedSessions + 1}\n          </Badge>\n        </CardHeader>\n\n        <CardContent className=\"space-y-6\">\n          {/* Mode Selector */}\n          <div className=\"flex justify-center gap-2\">\n            {(Object.keys(TIMER_DURATIONS) as TimerMode[]).map((timerMode) => (\n              <Button\n                key={timerMode}\n                variant={mode === timerMode ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() => switchMode(timerMode)}\n                disabled={isRunning}\n                className=\"text-xs\"\n              >\n                {MODE_LABELS[timerMode]}\n              </Button>\n            ))}\n          </div>\n\n          {/* Progress Ring */}\n          <div className=\"relative mx-auto h-48 w-48\">\n            <svg\n              className=\"h-full w-full -rotate-90 transform\"\n              viewBox=\"0 0 100 100\"\n            >\n              {/* Background circle */}\n              <circle\n                cx=\"50\"\n                cy=\"50\"\n                r=\"45\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                fill=\"none\"\n                className=\"text-muted-foreground/20\"\n              />\n              {/* Progress circle */}\n              <circle\n                cx=\"50\"\n                cy=\"50\"\n                r=\"45\"\n                stroke=\"currentColor\"\n                strokeWidth=\"3\"\n                fill=\"none\"\n                strokeDasharray={`${2 * Math.PI * 45}`}\n                strokeDashoffset={`${2 * Math.PI * 45 * (1 - progress / 100)}`}\n                className={`transition-all duration-1000 ${\n                  mode === \"work\"\n                    ? \"text-red-500\"\n                    : mode === \"shortBreak\"\n                      ? \"text-green-500\"\n                      : \"text-blue-500\"\n                }`}\n                strokeLinecap=\"round\"\n              />\n            </svg>\n\n            {/* Timer Display */}\n            <div className=\"absolute inset-0 flex flex-col items-center justify-center\">\n              <div className=\"font-mono text-4xl font-bold\">\n                {formatTime(timeLeft)}\n              </div>\n              <div className=\"mt-1 text-sm text-muted-foreground\">\n                {MODE_LABELS[mode]}\n              </div>\n            </div>\n          </div>\n\n          {/* Controls */}\n          <div className=\"flex justify-center gap-3\">\n            <Button\n              onClick={toggleTimer}\n              size=\"lg\"\n              className={`${MODE_COLORS[mode]} text-white hover:opacity-90`}\n            >\n              {isRunning ? (\n                <>\n                  <Pause className=\"mr-2 h-5 w-5\" />\n                  Pause\n                </>\n              ) : (\n                <>\n                  <Play className=\"mr-2 h-5 w-5\" />\n                  Start\n                </>\n              )}\n            </Button>\n\n            <Button onClick={resetTimer} variant=\"outline\" size=\"lg\">\n              <RotateCcw className=\"mr-2 h-5 w-5\" />\n              Reset\n            </Button>\n          </div>\n\n          {/* Stats */}\n          <div className=\"text-center text-sm text-muted-foreground\">\n            <p>Completed Sessions: {completedSessions}</p>\n            <p>\n              Next:{\" \"}\n              {completedSessions % 4 === 3\n                ? \"Long Break\"\n                : completedSessions % 2 === 0\n                  ? \"Short Break\"\n                  : \"Focus Time\"}\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"}